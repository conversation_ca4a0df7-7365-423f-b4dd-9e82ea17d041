# ESPOD - ESP32 Bluetooth A2DP MP3 Player

A high-performance ESP32-based Bluetooth audio player that streams MP3 files from SD card to TWS (True Wireless Stereo) earphones using A2DP (Advanced Audio Distribution Profile).

## Features

- **Threaded Audio Processing**: Dedicated FreeRTOS task for continuous audio streaming
- **Dual-Core Optimization**: Audio processing on Core 0, UI/controls on Core 1
- **Bluetooth A2DP Source**: Streams to TWS earphones and Bluetooth speakers
- **SD Card MP3 Playback**: Supports MP3 files from SD card
- **7-Button Joystick Control**: Complete navigation and playback control
- **Power Management**: Deep sleep mode with wake-on-button
- **LED Breathing Animation**: Visual feedback during operation

## Hardware Requirements

- ESP32 Development Board
- SD Card Module (CS pin 5)
- 7-But<PERSON> Joystick Module
- LED for status indication
- TWS earphones or Bluetooth speaker

## Pin Configuration

### 7-But<PERSON> Joystick
- UP: GPIO 32
- DOWN: GPIO 35
- LEFT: GPIO 34
- RIGHT: GPIO 39
- CENTER: GPIO 36
- SET: GPIO 25 (unused)
- RST: GPIO 33 (power on/off)

### Other Pins
- SD Card CS: GPIO 5
- LED: GPIO 2

## Threading Architecture

The system uses FreeRTOS tasks for optimal performance:

```
Core 0:
├── Audio Task (Priority 3, 4KB Stack) - Continuous MP3 decoding and A2DP streaming
└── Breather Task (Priority 1, 2KB Stack) - LED breathing animation

Core 1:
├── Main Loop - Device state management and UI
└── Button Monitor Task (Priority 2, 2KB Stack) - Button input handling
```

## Audio Processing

The audio system runs on a dedicated thread with the following flow:

1. **SD Card Reading**: AudioSourceSDFAT reads MP3 files
2. **MP3 Decoding**: Helix MP3 decoder processes audio data
3. **Buffer Management**: BufferRTOS handles audio buffering
4. **A2DP Streaming**: BluetoothA2DPSource transmits to Bluetooth devices

## Device States

- **OFF**: Deep sleep mode, wake with RST long press
- **INITIALIZING**: System startup
- **SEARCHING_TWS**: Scanning for Bluetooth devices
- **CONNECTING**: Establishing Bluetooth connection
- **CONNECTED**: Connected, ready to browse files
- **BROWSING_FILES**: File navigation mode
- **PLAYING**: Active audio playback
- **PAUSED**: Playback paused
- **STOPPED**: Playback stopped

## Button Controls

### Browsing Files
- UP/DOWN: Navigate through files
- CENTER: Start playing selected file

### During Playback
- UP/DOWN: Volume control
- LEFT/RIGHT: Previous/Next track
- CENTER: Play/Pause toggle

### Power Management
- RST Long Press: Power on/off toggle

## Build Instructions

### Prerequisites

1. Install PlatformIO Core or PlatformIO IDE
2. Ensure ESP32 platform is installed

### Building the Project

```bash
# Navigate to project directory
cd ESPOD/250413-185249-esp32dev

# Build the project
~/.platformio/penv/bin/platformio run

# Or if PlatformIO is in your PATH:
platformio run

# Upload to ESP32 (optional)
~/.platformio/penv/bin/platformio run --target upload

# Monitor serial output (optional)
~/.platformio/penv/bin/platformio device monitor
```

### Alternative Build Methods

If you have PlatformIO installed globally:
```bash
pio run
```

Using Python module:
```bash
python3 -m platformio run
```

### Build Configuration

The project uses the following configuration in `platformio.ini`:

- **Platform**: espressif32
- **Board**: esp32dev
- **Framework**: arduino
- **Monitor Speed**: 115200
- **Upload Speed**: 460800
- **Partition Scheme**: huge_app.csv

### Dependencies

The following libraries are automatically downloaded:
- arduino-libhelix (MP3 decoding)
- SdFat (SD card access)
- ESP32-A2DP (Bluetooth A2DP)
- arduino-audio-tools (Audio processing)

## Memory Usage

After successful build:
- **RAM Usage**: ~15.7% (51,592 bytes of 327,680 bytes)
- **Flash Usage**: ~46.3% (1,455,429 bytes of 3,145,728 bytes)

## Serial Output

Connect to serial monitor at 115200 baud to see:
- System initialization messages
- Bluetooth connection status
- Audio playback information
- Button press events
- Task status updates

## Troubleshooting

### Build Issues
- Ensure all dependencies are installed
- Check ESP32 platform version compatibility
- Verify PlatformIO installation

### Runtime Issues
- Check SD card formatting (FAT32 recommended)
- Verify MP3 file compatibility
- Ensure Bluetooth device is discoverable
- Check pin connections

### Audio Issues
- Verify A2DP compatibility of target device
- Check audio file format (MP3 supported)
- Monitor buffer status in serial output

## Development Notes

### Threading Implementation
The audio processing was moved to a separate FreeRTOS task to ensure:
- Continuous audio streaming without interruption
- Better system responsiveness
- Optimal dual-core CPU utilization
- Proper resource management during power cycles

### Code Organization
```
src/
├── main.cpp                 # Main application logic
├── audio/
│   ├── AudioManager.h       # Audio system interface
│   └── AudioManager.cpp     # Audio implementation with threading
├── ui/
│   ├── Buttons.h           # Button handling interface
│   └── Buttons.cpp         # Button task implementation
├── animations/
│   ├── Breather.h          # LED animation interface
│   └── Breather.cpp        # LED breathing task
├── core/
│   └── DeviceState.h       # System state definitions
└── utils/
    └── sleeper.cpp         # Power management utilities
```

## License

This project is open source. Please check individual library licenses for dependencies.
